package config

import (
	"encoding/json"
	"os"
	"path/filepath"
	"testing"

	"github.com/opencode-ai/opencode/internal/llm/models"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestMCPType_Constants(t *testing.T) {
	assert.Equal(t, <PERSON><PERSON><PERSON><PERSON>("stdio"), MCPStdio)
	assert.Equal(t, <PERSON><PERSON><PERSON><PERSON>("sse"), MCPSse)
}

func TestAgentName_Constants(t *testing.T) {
	assert.Equal(t, <PERSON><PERSON><PERSON>("coder"), AgentCoder)
	assert.Equal(t, <PERSON><PERSON><PERSON>("summarizer"), AgentSummarizer)
	assert.Equal(t, <PERSON><PERSON><PERSON>("task"), AgentTask)
	assert.Equal(t, <PERSON><PERSON><PERSON>("title"), AgentTitle)
}

func TestConfig_DefaultValues(t *testing.T) {
	config := &Config{
		Data:         Data{Directory: defaultDataDirectory},
		MCPServers:   make(map[string]MCPServer),
		Providers:    make(map[models.ModelProvider]Provider),
		LSP:          make(map[string]LSPConfig),
		Agents:       make(map[AgentName]Agent),
		ContextPaths: defaultContextPaths,
		TUI:          TUIConfig{Theme: "opencode"},
		AutoCompact:  true,
	}

	assert.Equal(t, defaultDataDirectory, config.Data.Directory)
	assert.NotNil(t, config.MCPServers)
	assert.NotNil(t, config.Providers)
	assert.NotNil(t, config.LSP)
	assert.NotNil(t, config.Agents)
	assert.Equal(t, defaultContextPaths, config.ContextPaths)
	assert.Equal(t, "opencode", config.TUI.Theme)
	assert.True(t, config.AutoCompact)
}

func TestMCPServer_JSONMarshaling(t *testing.T) {
	server := MCPServer{
		Command: "test-command",
		Env:     []string{"ENV1=value1", "ENV2=value2"},
		Args:    []string{"--arg1", "--arg2"},
		Type:    MCPStdio,
		URL:     "http://example.com",
		Headers: map[string]string{"Authorization": "Bearer token"},
	}

	// Marshal to JSON
	data, err := json.Marshal(server)
	require.NoError(t, err)

	// Unmarshal from JSON
	var unmarshaled MCPServer
	err = json.Unmarshal(data, &unmarshaled)
	require.NoError(t, err)

	assert.Equal(t, server, unmarshaled)
}

func TestAgent_JSONMarshaling(t *testing.T) {
	agent := Agent{
		Model:           models.Claude37Sonnet,
		MaxTokens:       4096,
		ReasoningEffort: "medium",
	}

	// Marshal to JSON
	data, err := json.Marshal(agent)
	require.NoError(t, err)

	// Unmarshal from JSON
	var unmarshaled Agent
	err = json.Unmarshal(data, &unmarshaled)
	require.NoError(t, err)

	assert.Equal(t, agent, unmarshaled)
}

func TestProvider_JSONMarshaling(t *testing.T) {
	provider := Provider{
		APIKey:   "test-api-key",
		Disabled: true,
	}

	// Marshal to JSON
	data, err := json.Marshal(provider)
	require.NoError(t, err)

	// Unmarshal from JSON
	var unmarshaled Provider
	err = json.Unmarshal(data, &unmarshaled)
	require.NoError(t, err)

	assert.Equal(t, provider, unmarshaled)
}

func TestHasAWSCredentials(t *testing.T) {
	// Store original environment
	originalEnvVars := map[string]string{
		"AWS_ACCESS_KEY_ID":                     os.Getenv("AWS_ACCESS_KEY_ID"),
		"AWS_SECRET_ACCESS_KEY":                 os.Getenv("AWS_SECRET_ACCESS_KEY"),
		"AWS_PROFILE":                           os.Getenv("AWS_PROFILE"),
		"AWS_DEFAULT_PROFILE":                   os.Getenv("AWS_DEFAULT_PROFILE"),
		"AWS_REGION":                            os.Getenv("AWS_REGION"),
		"AWS_DEFAULT_REGION":                    os.Getenv("AWS_DEFAULT_REGION"),
		"AWS_CONTAINER_CREDENTIALS_RELATIVE_URI": os.Getenv("AWS_CONTAINER_CREDENTIALS_RELATIVE_URI"),
		"AWS_CONTAINER_CREDENTIALS_FULL_URI":    os.Getenv("AWS_CONTAINER_CREDENTIALS_FULL_URI"),
	}

	// Clean environment
	for key := range originalEnvVars {
		os.Unsetenv(key)
	}

	// Restore environment after test
	defer func() {
		for key, value := range originalEnvVars {
			if value != "" {
				os.Setenv(key, value)
			} else {
				os.Unsetenv(key)
			}
		}
	}()

	tests := []struct {
		name     string
		envVars  map[string]string
		expected bool
	}{
		{
			name: "explicit AWS credentials",
			envVars: map[string]string{
				"AWS_ACCESS_KEY_ID":     "test-access-key",
				"AWS_SECRET_ACCESS_KEY": "test-secret-key",
			},
			expected: true,
		},
		{
			name: "AWS profile",
			envVars: map[string]string{
				"AWS_PROFILE": "test-profile",
			},
			expected: true,
		},
		{
			name: "AWS default profile",
			envVars: map[string]string{
				"AWS_DEFAULT_PROFILE": "test-profile",
			},
			expected: true,
		},
		{
			name: "AWS region",
			envVars: map[string]string{
				"AWS_REGION": "us-east-1",
			},
			expected: true,
		},
		{
			name: "AWS default region",
			envVars: map[string]string{
				"AWS_DEFAULT_REGION": "us-east-1",
			},
			expected: true,
		},
		{
			name: "container credentials relative URI",
			envVars: map[string]string{
				"AWS_CONTAINER_CREDENTIALS_RELATIVE_URI": "/v2/credentials/test",
			},
			expected: true,
		},
		{
			name: "container credentials full URI",
			envVars: map[string]string{
				"AWS_CONTAINER_CREDENTIALS_FULL_URI": "http://169.254.170.2/v2/credentials/test",
			},
			expected: true,
		},
		{
			name:     "no AWS credentials",
			envVars:  map[string]string{},
			expected: false,
		},
		{
			name: "incomplete explicit credentials",
			envVars: map[string]string{
				"AWS_ACCESS_KEY_ID": "test-access-key",
				// Missing AWS_SECRET_ACCESS_KEY
			},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Clean environment
			for key := range originalEnvVars {
				os.Unsetenv(key)
			}

			// Set test environment variables
			for key, value := range tt.envVars {
				os.Setenv(key, value)
			}

			result := hasAWSCredentials()
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestHasVertexAICredentials(t *testing.T) {
	// Store original environment
	originalEnvVars := map[string]string{
		"VERTEXAI_PROJECT":      os.Getenv("VERTEXAI_PROJECT"),
		"VERTEXAI_LOCATION":     os.Getenv("VERTEXAI_LOCATION"),
		"GOOGLE_CLOUD_PROJECT":  os.Getenv("GOOGLE_CLOUD_PROJECT"),
		"GOOGLE_CLOUD_REGION":   os.Getenv("GOOGLE_CLOUD_REGION"),
		"GOOGLE_CLOUD_LOCATION": os.Getenv("GOOGLE_CLOUD_LOCATION"),
	}

	// Clean environment
	for key := range originalEnvVars {
		os.Unsetenv(key)
	}

	// Restore environment after test
	defer func() {
		for key, value := range originalEnvVars {
			if value != "" {
				os.Setenv(key, value)
			} else {
				os.Unsetenv(key)
			}
		}
	}()

	tests := []struct {
		name     string
		envVars  map[string]string
		expected bool
	}{
		{
			name: "explicit VertexAI credentials",
			envVars: map[string]string{
				"VERTEXAI_PROJECT":  "test-project",
				"VERTEXAI_LOCATION": "us-central1",
			},
			expected: true,
		},
		{
			name: "Google Cloud project and region",
			envVars: map[string]string{
				"GOOGLE_CLOUD_PROJECT": "test-project",
				"GOOGLE_CLOUD_REGION":  "us-central1",
			},
			expected: true,
		},
		{
			name: "Google Cloud project and location",
			envVars: map[string]string{
				"GOOGLE_CLOUD_PROJECT":  "test-project",
				"GOOGLE_CLOUD_LOCATION": "us-central1",
			},
			expected: true,
		},
		{
			name:     "no VertexAI credentials",
			envVars:  map[string]string{},
			expected: false,
		},
		{
			name: "incomplete VertexAI credentials",
			envVars: map[string]string{
				"VERTEXAI_PROJECT": "test-project",
				// Missing VERTEXAI_LOCATION
			},
			expected: false,
		},
		{
			name: "incomplete Google Cloud credentials",
			envVars: map[string]string{
				"GOOGLE_CLOUD_PROJECT": "test-project",
				// Missing region/location
			},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Clean environment
			for key := range originalEnvVars {
				os.Unsetenv(key)
			}

			// Set test environment variables
			for key, value := range tt.envVars {
				os.Setenv(key, value)
			}

			result := hasVertexAICredentials()
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestGetProviderAPIKey(t *testing.T) {
	// Store original environment
	originalEnvVars := map[string]string{
		"ANTHROPIC_API_KEY":  os.Getenv("ANTHROPIC_API_KEY"),
		"OPENAI_API_KEY":     os.Getenv("OPENAI_API_KEY"),
		"GEMINI_API_KEY":     os.Getenv("GEMINI_API_KEY"),
		"GROQ_API_KEY":       os.Getenv("GROQ_API_KEY"),
		"AZURE_OPENAI_API_KEY": os.Getenv("AZURE_OPENAI_API_KEY"),
		"OPENROUTER_API_KEY": os.Getenv("OPENROUTER_API_KEY"),
	}

	// Clean environment
	for key := range originalEnvVars {
		os.Unsetenv(key)
	}

	// Restore environment after test
	defer func() {
		for key, value := range originalEnvVars {
			if value != "" {
				os.Setenv(key, value)
			} else {
				os.Unsetenv(key)
			}
		}
	}()

	tests := []struct {
		name     string
		provider models.ModelProvider
		envVar   string
		envValue string
		expected string
	}{
		{
			name:     "Anthropic API key",
			provider: models.ProviderAnthropic,
			envVar:   "ANTHROPIC_API_KEY",
			envValue: "test-anthropic-key",
			expected: "test-anthropic-key",
		},
		{
			name:     "OpenAI API key",
			provider: models.ProviderOpenAI,
			envVar:   "OPENAI_API_KEY",
			envValue: "test-openai-key",
			expected: "test-openai-key",
		},
		{
			name:     "Gemini API key",
			provider: models.ProviderGemini,
			envVar:   "GEMINI_API_KEY",
			envValue: "test-gemini-key",
			expected: "test-gemini-key",
		},
		{
			name:     "GROQ API key",
			provider: models.ProviderGROQ,
			envVar:   "GROQ_API_KEY",
			envValue: "test-groq-key",
			expected: "test-groq-key",
		},
		{
			name:     "Azure API key",
			provider: models.ProviderAzure,
			envVar:   "AZURE_OPENAI_API_KEY",
			envValue: "test-azure-key",
			expected: "test-azure-key",
		},
		{
			name:     "OpenRouter API key",
			provider: models.ProviderOpenRouter,
			envVar:   "OPENROUTER_API_KEY",
			envValue: "test-openrouter-key",
			expected: "test-openrouter-key",
		},
		{
			name:     "unknown provider",
			provider: models.ModelProvider("unknown"),
			envVar:   "",
			envValue: "",
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Clean environment
			for key := range originalEnvVars {
				os.Unsetenv(key)
			}

			// Set test environment variable
			if tt.envVar != "" {
				os.Setenv(tt.envVar, tt.envValue)
			}

			result := getProviderAPIKey(tt.provider)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestGetProviderAPIKey_AWSCredentials(t *testing.T) {
	// Store original environment
	originalEnvVars := map[string]string{
		"AWS_ACCESS_KEY_ID":     os.Getenv("AWS_ACCESS_KEY_ID"),
		"AWS_SECRET_ACCESS_KEY": os.Getenv("AWS_SECRET_ACCESS_KEY"),
	}

	// Clean environment
	for key := range originalEnvVars {
		os.Unsetenv(key)
	}

	// Restore environment after test
	defer func() {
		for key, value := range originalEnvVars {
			if value != "" {
				os.Setenv(key, value)
			} else {
				os.Unsetenv(key)
			}
		}
	}()

	t.Run("Bedrock with AWS credentials", func(t *testing.T) {
		os.Setenv("AWS_ACCESS_KEY_ID", "test-access-key")
		os.Setenv("AWS_SECRET_ACCESS_KEY", "test-secret-key")

		result := getProviderAPIKey(models.ProviderBedrock)
		assert.Equal(t, "aws-credentials-available", result)
	})

	t.Run("Bedrock without AWS credentials", func(t *testing.T) {
		// Clean all AWS environment variables
		awsEnvVars := []string{
			"AWS_ACCESS_KEY_ID", "AWS_SECRET_ACCESS_KEY", "AWS_PROFILE", 
			"AWS_DEFAULT_PROFILE", "AWS_REGION", "AWS_DEFAULT_REGION",
			"AWS_CONTAINER_CREDENTIALS_RELATIVE_URI", "AWS_CONTAINER_CREDENTIALS_FULL_URI",
		}
		for _, key := range awsEnvVars {
			os.Unsetenv(key)
		}
		
		result := getProviderAPIKey(models.ProviderBedrock)
		assert.Equal(t, "", result)
	})
}

func TestGetProviderAPIKey_VertexAICredentials(t *testing.T) {
	// Store original environment
	originalEnvVars := map[string]string{
		"VERTEXAI_PROJECT":  os.Getenv("VERTEXAI_PROJECT"),
		"VERTEXAI_LOCATION": os.Getenv("VERTEXAI_LOCATION"),
	}

	// Clean environment
	for key := range originalEnvVars {
		os.Unsetenv(key)
	}

	// Restore environment after test
	defer func() {
		for key, value := range originalEnvVars {
			if value != "" {
				os.Setenv(key, value)
			} else {
				os.Unsetenv(key)
			}
		}
	}()

	t.Run("VertexAI with credentials", func(t *testing.T) {
		os.Setenv("VERTEXAI_PROJECT", "test-project")
		os.Setenv("VERTEXAI_LOCATION", "us-central1")

		result := getProviderAPIKey(models.ProviderVertexAI)
		assert.Equal(t, "vertex-ai-credentials-available", result)
	})

	t.Run("VertexAI without credentials", func(t *testing.T) {
		// Clean all VertexAI environment variables
		vertexAIEnvVars := []string{
			"VERTEXAI_PROJECT", "VERTEXAI_LOCATION", "GOOGLE_CLOUD_PROJECT",
			"GOOGLE_CLOUD_REGION", "GOOGLE_CLOUD_LOCATION",
		}
		for _, key := range vertexAIEnvVars {
			os.Unsetenv(key)
		}
		
		result := getProviderAPIKey(models.ProviderVertexAI)
		assert.Equal(t, "", result)
	})
}

func TestLoadGitHubToken(t *testing.T) {
	// Store original environment
	originalToken := os.Getenv("GITHUB_TOKEN")
	defer func() {
		if originalToken != "" {
			os.Setenv("GITHUB_TOKEN", originalToken)
		} else {
			os.Unsetenv("GITHUB_TOKEN")
		}
	}()

	t.Run("from environment variable", func(t *testing.T) {
		testToken := "test-github-token"
		os.Setenv("GITHUB_TOKEN", testToken)

		token, err := LoadGitHubToken()
		require.NoError(t, err)
		assert.Equal(t, testToken, token)
	})

	t.Run("no environment variable and no config files", func(t *testing.T) {
		os.Unsetenv("GITHUB_TOKEN")
		
		// Override XDG_CONFIG_HOME to point to a non-existent directory
		tempDir, err := os.MkdirTemp("", "empty_config_test")
		require.NoError(t, err)
		defer os.RemoveAll(tempDir)
		
		originalXDGConfig := os.Getenv("XDG_CONFIG_HOME")
		os.Setenv("XDG_CONFIG_HOME", tempDir)
		defer func() {
			if originalXDGConfig != "" {
				os.Setenv("XDG_CONFIG_HOME", originalXDGConfig)
			} else {
				os.Unsetenv("XDG_CONFIG_HOME")
			}
		}()

		token, err := LoadGitHubToken()
		// If there's a GitHub token found somewhere in the system, it's not an error condition
		// Just skip this test
		if err == nil {
			t.Skip("GitHub token found in system, skipping test")
			return
		}
		assert.Error(t, err)
		assert.Empty(t, token)
		assert.Contains(t, err.Error(), "GitHub token not found")
	})
}

func TestLoadGitHubToken_ConfigFiles(t *testing.T) {
	// Store original environment
	originalToken := os.Getenv("GITHUB_TOKEN")
	originalXDGConfig := os.Getenv("XDG_CONFIG_HOME")

	defer func() {
		if originalToken != "" {
			os.Setenv("GITHUB_TOKEN", originalToken)
		} else {
			os.Unsetenv("GITHUB_TOKEN")
		}
		if originalXDGConfig != "" {
			os.Setenv("XDG_CONFIG_HOME", originalXDGConfig)
		} else {
			os.Unsetenv("XDG_CONFIG_HOME")
		}
	}()

	// Clear environment variable
	os.Unsetenv("GITHUB_TOKEN")

	// Create temporary directory for config
	tempDir, err := os.MkdirTemp("", "config_test")
	require.NoError(t, err)
	defer os.RemoveAll(tempDir)

	os.Setenv("XDG_CONFIG_HOME", tempDir)

	// Test hosts.json
	t.Run("from hosts.json", func(t *testing.T) {
		configDir := filepath.Join(tempDir, "github-copilot")
		err := os.MkdirAll(configDir, 0755)
		require.NoError(t, err)

		hostsConfig := map[string]map[string]interface{}{
			"github.com": {
				"oauth_token": "test-oauth-token-hosts",
			},
		}

		hostsData, err := json.Marshal(hostsConfig)
		require.NoError(t, err)

		hostsFile := filepath.Join(configDir, "hosts.json")
		err = os.WriteFile(hostsFile, hostsData, 0644)
		require.NoError(t, err)

		token, err := LoadGitHubToken()
		require.NoError(t, err)
		assert.Equal(t, "test-oauth-token-hosts", token)

		// Clean up for next test
		os.Remove(hostsFile)
	})

	// Test apps.json
	t.Run("from apps.json", func(t *testing.T) {
		configDir := filepath.Join(tempDir, "github-copilot")
		err := os.MkdirAll(configDir, 0755)
		require.NoError(t, err)

		appsConfig := map[string]map[string]interface{}{
			"api.github.com": {
				"oauth_token": "test-oauth-token-apps",
			},
		}

		appsData, err := json.Marshal(appsConfig)
		require.NoError(t, err)

		appsFile := filepath.Join(configDir, "apps.json")
		err = os.WriteFile(appsFile, appsData, 0644)
		require.NoError(t, err)

		token, err := LoadGitHubToken()
		require.NoError(t, err)
		assert.Equal(t, "test-oauth-token-apps", token)
	})
}

func TestDefaultContextPaths(t *testing.T) {
	expectedPaths := []string{
		".github/copilot-instructions.md",
		".cursorrules",
		".cursor/rules/",
		"CLAUDE.md",
		"CLAUDE.local.md",
		"opencode.md",
		"opencode.local.md",
		"OpenCode.md",
		"OpenCode.local.md",
		"OPENCODE.md",
		"OPENCODE.local.md",
	}

	assert.Equal(t, expectedPaths, defaultContextPaths)
	assert.Len(t, defaultContextPaths, len(expectedPaths))
}

func TestConstants(t *testing.T) {
	assert.Equal(t, ".opencode", defaultDataDirectory)
	assert.Equal(t, "info", defaultLogLevel)
	assert.Equal(t, "opencode", appName)
	assert.Equal(t, int64(4096), int64(MaxTokensFallbackDefault))
}

func TestGet_NilConfig(t *testing.T) {
	// Save current config
	originalCfg := cfg
	defer func() {
		cfg = originalCfg
	}()

	// Set config to nil
	cfg = nil

	result := Get()
	assert.Nil(t, result)
}

func TestWorkingDirectory_PanicsOnNilConfig(t *testing.T) {
	// Save current config
	originalCfg := cfg
	defer func() {
		cfg = originalCfg
	}()

	// Set config to nil
	cfg = nil

	assert.Panics(t, func() {
		WorkingDirectory()
	})
}

func TestConfig_JSONMarshaling(t *testing.T) {
	config := &Config{
		Data: Data{
			Directory: "/test/data",
		},
		WorkingDir: "/test/working",
		MCPServers: map[string]MCPServer{
			"test": {
				Command: "test-command",
				Type:    MCPStdio,
			},
		},
		Providers: map[models.ModelProvider]Provider{
			models.ProviderOpenAI: {
				APIKey:   "test-key",
				Disabled: false,
			},
		},
		Agents: map[AgentName]Agent{
			AgentCoder: {
				Model:     models.GPT41,
				MaxTokens: 4096,
			},
		},
		Debug:        true,
		DebugLSP:     false,
		ContextPaths: []string{"test.md"},
		TUI: TUIConfig{
			Theme: "test-theme",
		},
		Shell: ShellConfig{
			Path: "/bin/bash",
			Args: []string{"-l"},
		},
		AutoCompact: true,
	}

	// Marshal to JSON
	data, err := json.Marshal(config)
	require.NoError(t, err)

	// Unmarshal from JSON
	var unmarshaled Config
	err = json.Unmarshal(data, &unmarshaled)
	require.NoError(t, err)

	assert.Equal(t, config.Data, unmarshaled.Data)
	assert.Equal(t, config.WorkingDir, unmarshaled.WorkingDir)
	assert.Equal(t, config.Debug, unmarshaled.Debug)
	assert.Equal(t, config.DebugLSP, unmarshaled.DebugLSP)
	assert.Equal(t, config.ContextPaths, unmarshaled.ContextPaths)
	assert.Equal(t, config.TUI, unmarshaled.TUI)
	assert.Equal(t, config.Shell, unmarshaled.Shell)
	assert.Equal(t, config.AutoCompact, unmarshaled.AutoCompact)
}

func TestLSPConfig_JSONMarshaling(t *testing.T) {
	lspConfig := LSPConfig{
		Disabled: false,
		Command:  "gopls",
		Args:     []string{"serve"},
		Options:  map[string]interface{}{"analyses": map[string]bool{"unusedparams": true}},
	}

	// Marshal to JSON
	data, err := json.Marshal(lspConfig)
	require.NoError(t, err)

	// Unmarshal from JSON
	var unmarshaled LSPConfig
	err = json.Unmarshal(data, &unmarshaled)
	require.NoError(t, err)

	assert.Equal(t, lspConfig.Disabled, unmarshaled.Disabled)
	assert.Equal(t, lspConfig.Command, unmarshaled.Command)
	assert.Equal(t, lspConfig.Args, unmarshaled.Args)
	// Options comparison needs special handling due to interface{} type
	assert.NotNil(t, unmarshaled.Options)
}

func TestTUIConfig_JSONMarshaling(t *testing.T) {
	tuiConfig := TUIConfig{
		Theme: "dark",
	}

	// Marshal to JSON
	data, err := json.Marshal(tuiConfig)
	require.NoError(t, err)

	// Unmarshal from JSON
	var unmarshaled TUIConfig
	err = json.Unmarshal(data, &unmarshaled)
	require.NoError(t, err)

	assert.Equal(t, tuiConfig, unmarshaled)
}

func TestShellConfig_JSONMarshaling(t *testing.T) {
	shellConfig := ShellConfig{
		Path: "/bin/zsh",
		Args: []string{"-l", "-i"},
	}

	// Marshal to JSON
	data, err := json.Marshal(shellConfig)
	require.NoError(t, err)

	// Unmarshal from JSON
	var unmarshaled ShellConfig
	err = json.Unmarshal(data, &unmarshaled)
	require.NoError(t, err)

	assert.Equal(t, shellConfig, unmarshaled)
}

func TestData_JSONMarshaling(t *testing.T) {
	data := Data{
		Directory: "/custom/data/dir",
	}

	// Marshal to JSON
	jsonData, err := json.Marshal(data)
	require.NoError(t, err)

	// Unmarshal from JSON
	var unmarshaled Data
	err = json.Unmarshal(jsonData, &unmarshaled)
	require.NoError(t, err)

	assert.Equal(t, data, unmarshaled)
}


