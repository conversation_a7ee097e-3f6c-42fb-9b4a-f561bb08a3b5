package version

import (
	"runtime/debug"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestVersion_DefaultValue(t *testing.T) {
	// Test that Version has a default value
	// The actual value depends on how the binary was built
	assert.NotEmpty(t, Version, "Version should not be empty")
}

func TestVersionBehavior(t *testing.T) {
	// Save original version
	originalVersion := Version
	defer func() {
		Version = originalVersion
	}()

	tests := []struct {
		name            string
		initialVersion  string
		buildInfoResult *debug.BuildInfo
		buildInfoOk     bool
		expectedVersion string
	}{
		{
			name:           "version already set by ldflags",
			initialVersion: "v1.2.3",
			buildInfoResult: &debug.BuildInfo{
				Main: debug.Module{Version: "v1.0.0"},
			},
			buildInfoOk:     true,
			expectedVersion: "v1.2.3", // Should not change
		},
		{
			name:           "version unknown with valid build info",
			initialVersion: "unknown",
			buildInfoResult: &debug.BuildInfo{
				Main: debug.Module{Version: "v1.0.0"},
			},
			buildInfoOk:     true,
			expectedVersion: "v1.0.0", // Should be updated from build info
		},
		{
			name:           "version unknown with devel build info",
			initialVersion: "unknown",
			buildInfoResult: &debug.BuildInfo{
				Main: debug.Module{Version: "(devel)"},
			},
			buildInfoOk:     true,
			expectedVersion: "unknown", // Should remain unknown
		},
		{
			name:           "version unknown with empty build info",
			initialVersion: "unknown",
			buildInfoResult: &debug.BuildInfo{
				Main: debug.Module{Version: ""},
			},
			buildInfoOk:     true,
			expectedVersion: "unknown", // Should remain unknown
		},
		{
			name:            "version unknown with no build info",
			initialVersion:  "unknown",
			buildInfoResult: nil,
			buildInfoOk:     false,
			expectedVersion: "unknown", // Should remain unknown
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Reset version
			Version = tt.initialVersion

			// Simulate the init function logic
			if Version == "unknown" {
				if tt.buildInfoOk {
					info := tt.buildInfoResult
					mainVersion := info.Main.Version
					if mainVersion != "" && mainVersion != "(devel)" {
						Version = mainVersion
					}
				}
			}

			assert.Equal(t, tt.expectedVersion, Version)
		})
	}
}

func TestVersion_ActualBuildInfo(t *testing.T) {
	// Test with actual build info from the current binary
	info, ok := debug.ReadBuildInfo()
	if !ok {
		t.Skip("Build info not available (Go version < 1.18 or not built with go install)")
		return
	}

	// Verify that build info is structured as expected
	assert.NotNil(t, info)
	assert.NotNil(t, info.Main)
	
	// The version could be various values depending on how the test is run:
	// - "(devel)" for go run or go test
	// - A version string for go install
	// - Empty string in some cases
	t.Logf("Build info version: %s", info.Main.Version)
	t.Logf("Current Version variable: %s", Version)
}

func TestVersion_CanBeModified(t *testing.T) {
	// Test that the Version variable can be modified
	// This is important for testing and for ldflags injection
	originalVersion := Version
	defer func() {
		Version = originalVersion
	}()

	testVersion := "test-version-123"
	Version = testVersion
	
	assert.Equal(t, testVersion, Version)
}

func TestVersion_StringType(t *testing.T) {
	// Verify that Version is a string type
	assert.IsType(t, "", Version)
}

func TestVersion_NotEmpty(t *testing.T) {
	// In normal circumstances, Version should not be empty
	// It should either be set by ldflags or by the init function
	assert.NotEmpty(t, Version, "Version should be set to some value")
}

func TestInitFunction_Coverage(t *testing.T) {
	// This test ensures we have coverage of the init function logic
	// We can't directly test the init function since it runs before tests,
	// but we can test the logic it implements

	// Save original version
	originalVersion := Version
	defer func() {
		Version = originalVersion
	}()

	// Test scenario where version is "unknown" and we have build info
	Version = "unknown"
	
	info, ok := debug.ReadBuildInfo()
	if ok {
		mainVersion := info.Main.Version
		if mainVersion != "" && mainVersion != "(devel)" {
			// This is what the init function would do
			Version = mainVersion
			assert.Equal(t, mainVersion, Version)
		} else {
			// Version should remain "unknown"
			assert.Equal(t, "unknown", Version)
		}
	} else {
		// If build info is not available, version should remain "unknown"
		assert.Equal(t, "unknown", Version)
	}
}

func TestVersion_BuildInfoEdgeCases(t *testing.T) {
	// Test various edge cases with build info
	originalVersion := Version
	defer func() {
		Version = originalVersion
	}()

	testCases := []struct {
		name           string
		moduleVersion  string
		shouldUpdate   bool
		expectedResult string
	}{
		{
			name:           "valid semantic version",
			moduleVersion:  "v1.2.3",
			shouldUpdate:   true,
			expectedResult: "v1.2.3",
		},
		{
			name:           "valid version without v prefix",
			moduleVersion:  "1.2.3",
			shouldUpdate:   true,
			expectedResult: "1.2.3",
		},
		{
			name:           "development version",
			moduleVersion:  "(devel)",
			shouldUpdate:   false,
			expectedResult: "unknown",
		},
		{
			name:           "empty version",
			moduleVersion:  "",
			shouldUpdate:   false,
			expectedResult: "unknown",
		},
		{
			name:           "pseudo version",
			moduleVersion:  "v0.0.0-20210101000000-abcdef123456",
			shouldUpdate:   true,
			expectedResult: "v0.0.0-20210101000000-abcdef123456",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			Version = "unknown"
			
			// Simulate the init function logic
			if tc.moduleVersion != "" && tc.moduleVersion != "(devel)" {
				Version = tc.moduleVersion
			}
			
			assert.Equal(t, tc.expectedResult, Version)
		})
	}
}

func TestVersion_PackageImport(t *testing.T) {
	// Test that the package can be imported and Version is accessible
	// This test ensures the package exports are correct
	assert.NotNil(t, &Version)
	
	// Test that we can read and write to Version
	original := Version
	Version = "test"
	assert.Equal(t, "test", Version)
	Version = original
}

func TestVersion_ThreadSafety(t *testing.T) {
	// While the Version variable is not explicitly thread-safe,
	// in practice it's only written once during init and then read-only
	// This test documents this behavior
	
	// Multiple goroutines reading Version should work fine
	done := make(chan struct{}, 10)
	
	for i := 0; i < 10; i++ {
		go func() {
			defer func() { done <- struct{}{} }()
			// Just reading Version should be safe
			version := Version
			assert.NotEmpty(t, version)
		}()
	}
	
	// Wait for all goroutines to complete
	for i := 0; i < 10; i++ {
		<-done
	}
}
