package format

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestOutputFormat_String(t *testing.T) {
	tests := []struct {
		name     string
		format   OutputFormat
		expected string
	}{
		{
			name:     "text format",
			format:   Text,
			expected: "text",
		},
		{
			name:     "json format",
			format:   JSON,
			expected: "json",
		},
		{
			name:     "custom format",
			format:   OutputFormat("custom"),
			expected: "custom",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.format.String()
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestParse(t *testing.T) {
	tests := []struct {
		name        string
		input       string
		expected    OutputFormat
		expectError bool
	}{
		{
			name:        "valid text format",
			input:       "text",
			expected:    Text,
			expectError: false,
		},
		{
			name:        "valid json format",
			input:       "json",
			expected:    JSO<PERSON>,
			expectError: false,
		},
		{
			name:        "uppercase text",
			input:       "TEXT",
			expected:    Text,
			expectError: false,
		},
		{
			name:        "uppercase json",
			input:       "JSON",
			expected:    JSON,
			expectError: false,
		},
		{
			name:        "mixed case text",
			input:       "Text",
			expected:    Text,
			expectError: false,
		},
		{
			name:        "mixed case json",
			input:       "Json",
			expected:    JSON,
			expectError: false,
		},
		{
			name:        "text with whitespace",
			input:       "  text  ",
			expected:    Text,
			expectError: false,
		},
		{
			name:        "json with whitespace",
			input:       "  json  ",
			expected:    JSON,
			expectError: false,
		},
		{
			name:        "invalid format",
			input:       "xml",
			expected:    "",
			expectError: true,
		},
		{
			name:        "empty string",
			input:       "",
			expected:    "",
			expectError: true,
		},
		{
			name:        "whitespace only",
			input:       "   ",
			expected:    "",
			expectError: true,
		},
		{
			name:        "invalid format with special chars",
			input:       "text/json",
			expected:    "",
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := Parse(tt.input)

			if tt.expectError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "invalid format")
				assert.Equal(t, OutputFormat(""), result)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestIsValid(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected bool
	}{
		{
			name:     "valid text",
			input:    "text",
			expected: true,
		},
		{
			name:     "valid json",
			input:    "json",
			expected: true,
		},
		{
			name:     "uppercase text",
			input:    "TEXT",
			expected: true,
		},
		{
			name:     "uppercase json",
			input:    "JSON",
			expected: true,
		},
		{
			name:     "mixed case",
			input:    "Text",
			expected: true,
		},
		{
			name:     "with whitespace",
			input:    "  json  ",
			expected: true,
		},
		{
			name:     "invalid format",
			input:    "xml",
			expected: false,
		},
		{
			name:     "empty string",
			input:    "",
			expected: false,
		},
		{
			name:     "whitespace only",
			input:    "   ",
			expected: false,
		},
		{
			name:     "special characters",
			input:    "text/json",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsValid(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestGetHelpText(t *testing.T) {
	helpText := GetHelpText()
	
	assert.Contains(t, helpText, "Supported output formats:")
	assert.Contains(t, helpText, "text: Plain text output (default)")
	assert.Contains(t, helpText, "json: Output wrapped in a JSON object")
	assert.NotEmpty(t, helpText)
}

func TestFormatOutput(t *testing.T) {
	tests := []struct {
		name        string
		content     string
		formatStr   string
		expectJSON  bool
		expectError bool
	}{
		{
			name:        "text format",
			content:     "Hello, World!",
			formatStr:   "text",
			expectJSON:  false,
			expectError: false,
		},
		{
			name:        "json format",
			content:     "Hello, World!",
			formatStr:   "json",
			expectJSON:  true,
			expectError: false,
		},
		{
			name:        "uppercase json",
			content:     "Hello, World!",
			formatStr:   "JSON",
			expectJSON:  true,
			expectError: false,
		},
		{
			name:        "invalid format defaults to text",
			content:     "Hello, World!",
			formatStr:   "xml",
			expectJSON:  false,
			expectError: false,
		},
		{
			name:        "empty format defaults to text",
			content:     "Hello, World!",
			formatStr:   "",
			expectJSON:  false,
			expectError: false,
		},
		{
			name:        "multiline content as json",
			content:     "Line 1\nLine 2\nLine 3",
			formatStr:   "json",
			expectJSON:  true,
			expectError: false,
		},
		{
			name:        "content with quotes as json",
			content:     `He said "Hello"`,
			formatStr:   "json",
			expectJSON:  true,
			expectError: false,
		},
		{
			name:        "content with special characters as json",
			content:     "Hello\t\r\n\\ \"World\"",
			formatStr:   "json",
			expectJSON:  true,
			expectError: false,
		},
		{
			name:        "empty content as json",
			content:     "",
			formatStr:   "json",
			expectJSON:  true,
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := FormatOutput(tt.content, tt.formatStr)

			if tt.expectJSON {
				// Verify it's valid JSON
				var jsonData map[string]interface{}
				err := json.Unmarshal([]byte(result), &jsonData)
				require.NoError(t, err, "Result should be valid JSON")

				// Verify the structure
				response, exists := jsonData["response"]
				require.True(t, exists, "JSON should have 'response' field")
				assert.Equal(t, tt.content, response, "JSON response should match original content")
			} else {
				// For text format, result should equal original content
				assert.Equal(t, tt.content, result)
			}
		})
	}
}

func TestFormatAsJSON(t *testing.T) {
	tests := []struct {
		name     string
		content  string
		expected map[string]interface{}
	}{
		{
			name:    "simple string",
			content: "Hello, World!",
			expected: map[string]interface{}{
				"response": "Hello, World!",
			},
		},
		{
			name:    "empty string",
			content: "",
			expected: map[string]interface{}{
				"response": "",
			},
		},
		{
			name:    "multiline string",
			content: "Line 1\nLine 2\nLine 3",
			expected: map[string]interface{}{
				"response": "Line 1\nLine 2\nLine 3",
			},
		},
		{
			name:    "string with quotes",
			content: `He said "Hello"`,
			expected: map[string]interface{}{
				"response": `He said "Hello"`,
			},
		},
		{
			name:    "string with special characters",
			content: "Hello\t\r\n\\ \"World\"",
			expected: map[string]interface{}{
				"response": "Hello\t\r\n\\ \"World\"",
			},
		},
		{
			name:    "string with unicode",
			content: "Hello 世界 🌍",
			expected: map[string]interface{}{
				"response": "Hello 世界 🌍",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := formatAsJSON(tt.content)

			// Verify it's valid JSON
			var jsonData map[string]interface{}
			err := json.Unmarshal([]byte(result), &jsonData)
			require.NoError(t, err, "Result should be valid JSON")

			// Verify the structure and content
			assert.Equal(t, tt.expected, jsonData)

			// Verify formatting (should be indented)
			assert.Contains(t, result, "  \"response\":", "JSON should be properly indented")
		})
	}
}

func TestSupportedFormats(t *testing.T) {
	expectedFormats := []string{"text", "json"}
	
	assert.Equal(t, expectedFormats, SupportedFormats)
	assert.Len(t, SupportedFormats, 2)
	assert.Contains(t, SupportedFormats, "text")
	assert.Contains(t, SupportedFormats, "json")
}

func TestConstants(t *testing.T) {
	// Test that constants have expected values
	assert.Equal(t, OutputFormat("text"), Text)
	assert.Equal(t, OutputFormat("json"), JSON)
	
	// Test that constants are consistent with string representations
	assert.Equal(t, "text", Text.String())
	assert.Equal(t, "json", JSON.String())
}

func TestFormatOutput_EdgeCases(t *testing.T) {
	// Test with very large content
	t.Run("large content", func(t *testing.T) {
		largeContent := string(make([]byte, 10000))
		for i := range largeContent {
			largeContent = largeContent[:i] + "a" + largeContent[i+1:]
		}
		
		result := FormatOutput(largeContent, "json")
		
		var jsonData map[string]interface{}
		err := json.Unmarshal([]byte(result), &jsonData)
		require.NoError(t, err)
		assert.Equal(t, largeContent, jsonData["response"])
	})

	// Test with content that could break JSON encoding
	t.Run("problematic json content", func(t *testing.T) {
		problemContent := "\x00\x01\x02\x03\x04\x05"
		
		result := FormatOutput(problemContent, "json")
		
		// Should still produce valid JSON (using fallback if needed)
		var jsonData map[string]interface{}
		err := json.Unmarshal([]byte(result), &jsonData)
		require.NoError(t, err)
	})
}

func TestFormatOutput_FallbackMechanism(t *testing.T) {
	// Test that JSON formatting gracefully handles edge cases
	// by checking the manual escaping fallback path
	
	// This is hard to test directly since json.MarshalIndent is quite robust
	// But we can test the manual escaping logic by examining the formatAsJSON function behavior
	
	content := "test\nwith\ttabs\rand\"quotes\\"
	result := formatAsJSON(content)
	
	var jsonData map[string]interface{}
	err := json.Unmarshal([]byte(result), &jsonData)
	require.NoError(t, err)
	assert.Equal(t, content, jsonData["response"])
}

func TestOutputFormat_JSONMarshaling(t *testing.T) {
	// Test that OutputFormat can be marshaled/unmarshaled as JSON
	tests := []struct {
		name   string
		format OutputFormat
	}{
		{"text format", Text},
		{"json format", JSON},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Marshal
			data, err := json.Marshal(tt.format)
			require.NoError(t, err)
			
			// Unmarshal
			var format OutputFormat
			err = json.Unmarshal(data, &format)
			require.NoError(t, err)
			
			assert.Equal(t, tt.format, format)
		})
	}
}
