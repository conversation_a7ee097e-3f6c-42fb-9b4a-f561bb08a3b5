package fileutil

import (
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestGetRgCmd(t *testing.T) {
	// Store original rgPath
	originalRgPath := rgPath

	tests := []struct {
		name        string
		globPattern string
		rgPath      string
		expected    []string
		expectNil   bool
	}{
		{
			name:        "empty pattern",
			globPattern: "",
			rgPath:      "/usr/bin/rg",
			expected:    []string{"/usr/bin/rg", "--files", "-L", "--null"},
			expectNil:   false,
		},
		{
			name:        "with pattern",
			globPattern: "*.go",
			rgPath:      "/usr/bin/rg",
			expected:    []string{"/usr/bin/rg", "--files", "-L", "--null", "--glob", "/*.go"},
			expectNil:   false,
		},
		{
			name:        "pattern with leading slash",
			globPattern: "/*.go",
			rgPath:      "/usr/bin/rg",
			expected:    []string{"/usr/bin/rg", "--files", "-L", "--null", "--glob", "/*.go"},
			expectNil:   false,
		},
		{
			name:        "absolute pattern",
			globPattern: "/tmp/*.go",
			rgPath:      "/usr/bin/rg",
			expected:    []string{"/usr/bin/rg", "--files", "-L", "--null", "--glob", "/tmp/*.go"},
			expectNil:   false,
		},
		{
			name:        "no rg binary",
			globPattern: "*.go",
			rgPath:      "",
			expected:    nil,
			expectNil:   true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Set rgPath for this test
			rgPath = tt.rgPath

			cmd := GetRgCmd(tt.globPattern)

			if tt.expectNil {
				assert.Nil(t, cmd)
				return
			}

			require.NotNil(t, cmd)
			assert.Equal(t, tt.expected, cmd.Args)
			assert.Equal(t, ".", cmd.Dir)
		})
	}

	// Restore original rgPath
	rgPath = originalRgPath
}

func TestGetFzfCmd(t *testing.T) {
	// Store original fzfPath
	originalFzfPath := fzfPath

	tests := []struct {
		name      string
		query     string
		fzfPath   string
		expected  []string
		expectNil bool
	}{
		{
			name:      "with query",
			query:     "test",
			fzfPath:   "/usr/bin/fzf",
			expected:  []string{"/usr/bin/fzf", "--filter", "test", "--read0", "--print0"},
			expectNil: false,
		},
		{
			name:      "empty query",
			query:     "",
			fzfPath:   "/usr/bin/fzf",
			expected:  []string{"/usr/bin/fzf", "--filter", "", "--read0", "--print0"},
			expectNil: false,
		},
		{
			name:      "no fzf binary",
			query:     "test",
			fzfPath:   "",
			expected:  nil,
			expectNil: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Set fzfPath for this test
			fzfPath = tt.fzfPath

			cmd := GetFzfCmd(tt.query)

			if tt.expectNil {
				assert.Nil(t, cmd)
				return
			}

			require.NotNil(t, cmd)
			assert.Equal(t, tt.expected, cmd.Args)
			assert.Equal(t, ".", cmd.Dir)
		})
	}

	// Restore original fzfPath
	fzfPath = originalFzfPath
}

func TestSkipHidden(t *testing.T) {
	tests := []struct {
		name     string
		path     string
		expected bool
	}{
		// Hidden files (starting with dot)
		{
			name:     "hidden file",
			path:     ".hidden",
			expected: true,
		},
		{
			name:     "hidden file with extension",
			path:     ".gitignore",
			expected: true,
		},
		{
			name:     "dot directory",
			path:     "dir/.hidden",
			expected: true,
		},
		{
			name:     "current directory",
			path:     ".",
			expected: false,
		},
		{
			name:     "parent directory",
			path:     "..",
			expected: true,
		},

		// Common ignored directories
		{
			name:     "opencode directory",
			path:     ".opencode",
			expected: true,
		},
		{
			name:     "node_modules directory",
			path:     "node_modules",
			expected: true,
		},
		{
			name:     "nested node_modules",
			path:     "src/node_modules/test",
			expected: true,
		},
		{
			name:     "vendor directory",
			path:     "vendor",
			expected: true,
		},
		{
			name:     "dist directory",
			path:     "dist",
			expected: true,
		},
		{
			name:     "build directory",
			path:     "build",
			expected: true,
		},
		{
			name:     "target directory",
			path:     "target",
			expected: true,
		},
		{
			name:     "git directory",
			path:     ".git",
			expected: true,
		},
		{
			name:     "nested git directory",
			path:     "project/.git/config",
			expected: true,
		},
		{
			name:     "idea directory",
			path:     ".idea",
			expected: true,
		},
		{
			name:     "vscode directory",
			path:     ".vscode",
			expected: true,
		},
		{
			name:     "pycache directory",
			path:     "__pycache__",
			expected: true,
		},
		{
			name:     "bin directory",
			path:     "bin",
			expected: true,
		},
		{
			name:     "obj directory",
			path:     "obj",
			expected: true,
		},
		{
			name:     "out directory",
			path:     "out",
			expected: true,
		},
		{
			name:     "coverage directory",
			path:     "coverage",
			expected: true,
		},
		{
			name:     "tmp directory",
			path:     "tmp",
			expected: true,
		},
		{
			name:     "temp directory",
			path:     "temp",
			expected: true,
		},
		{
			name:     "logs directory",
			path:     "logs",
			expected: true,
		},
		{
			name:     "generated directory",
			path:     "generated",
			expected: true,
		},
		{
			name:     "bower_components directory",
			path:     "bower_components",
			expected: true,
		},
		{
			name:     "jspm_packages directory",
			path:     "jspm_packages",
			expected: true,
		},

		// Regular files and directories
		{
			name:     "regular file",
			path:     "main.go",
			expected: false,
		},
		{
			name:     "regular directory",
			path:     "src",
			expected: false,
		},
		{
			name:     "nested regular path",
			path:     "src/main.go",
			expected: false,
		},
		{
			name:     "similar to ignored but not exact",
			path:     "builds",
			expected: false,
		},
		{
			name:     "similar to ignored but not exact 2",
			path:     "distribution",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := SkipHidden(tt.path)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestGlobWithDoublestar(t *testing.T) {
	// Create a temporary directory for testing
	tempDir, err := os.MkdirTemp("", "fileutil_test")
	require.NoError(t, err)
	defer os.RemoveAll(tempDir)

	// Create test files
	testFiles := []string{
		"test.go",
		"main.go",
		"src/utils.go",
		"src/helper.go",
		"dist/build.js",
		".hidden.go",
		"node_modules/package.json",
	}

	for _, file := range testFiles {
		fullPath := filepath.Join(tempDir, file)
		err := os.MkdirAll(filepath.Dir(fullPath), 0755)
		require.NoError(t, err)
		err = os.WriteFile(fullPath, []byte("test content"), 0644)
		require.NoError(t, err)
	}

	tests := []struct {
		name          string
		pattern       string
		searchPath    string
		limit         int
		expectedCount int
		expectedFiles []string
		expectTrunc   bool
		expectError   bool
	}{
		{
			name:          "all go files",
			pattern:       "**/*.go",
			searchPath:    tempDir,
			limit:         0,
			expectedCount: 4, // test.go, main.go, src/utils.go, src/helper.go (excluding .hidden.go)
			expectedFiles: []string{"test.go", "main.go", "src/utils.go", "src/helper.go"},
			expectTrunc:   false,
			expectError:   false,
		},
		{
			name:          "with high limit",
			pattern:       "**/*.go",
			searchPath:    tempDir,
			limit:         6, // Use a higher limit since we have 4 files, this won't trigger the error
			expectedCount: 4,
			expectedFiles: []string{}, // Order depends on modification time
			expectTrunc:   false,
			expectError:   false,
		},
		{
			name:          "with low limit",
			pattern:       "**/*.go",
			searchPath:    tempDir,
			limit:         2, // Will cause truncation
			expectedCount: 2,
			expectedFiles: []string{}, // Order depends on modification time
			expectTrunc:   true,
			expectError:   true, // fs.SkipAll causes an error when limit*2 is reached
		},
		{
			name:          "specific directory",
			pattern:       "src/*.go",
			searchPath:    tempDir,
			limit:         0,
			expectedCount: 2,
			expectedFiles: []string{"src/utils.go", "src/helper.go"},
			expectTrunc:   false,
			expectError:   false,
		},
		{
			name:          "no matches",
			pattern:       "**/*.py",
			searchPath:    tempDir,
			limit:         0,
			expectedCount: 0,
			expectedFiles: []string{},
			expectTrunc:   false,
			expectError:   false,
		},
		{
			name:          "pattern with leading slash",
			pattern:       "/*.go",
			searchPath:    tempDir,
			limit:         0,
			expectedCount: 2, // test.go, main.go
			expectedFiles: []string{"test.go", "main.go"},
			expectTrunc:   false,
			expectError:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			results, truncated, err := GlobWithDoublestar(tt.pattern, tt.searchPath, tt.limit)

			if tt.expectError {
				assert.Error(t, err)
				return
			}

			require.NoError(t, err)
			assert.Equal(t, tt.expectTrunc, truncated)
			assert.Equal(t, tt.expectedCount, len(results))

			if len(tt.expectedFiles) > 0 {
				// Convert results to relative paths for comparison
				var relativePaths []string
				for _, result := range results {
					relPath, err := filepath.Rel(tt.searchPath, result)
					require.NoError(t, err)
					relativePaths = append(relativePaths, relPath)
				}

				// Sort both slices for comparison
				assert.ElementsMatch(t, tt.expectedFiles, relativePaths)
			}
		})
	}
}

func TestGlobWithDoublestar_EdgeCases(t *testing.T) {
	// Test with non-existent directory
	t.Run("non-existent directory", func(t *testing.T) {
		results, truncated, err := GlobWithDoublestar("**/*.go", "/non/existent/path", 0)
		// os.DirFS on non-existent path may not error immediately, it errors during walk
		// So we expect either an error OR empty results
		if err != nil {
			assert.False(t, truncated)
			assert.Nil(t, results)
		} else {
			assert.Empty(t, results)
		}
	})

	// Test with empty pattern
	t.Run("empty pattern", func(t *testing.T) {
		tempDir, err := os.MkdirTemp("", "fileutil_test")
		require.NoError(t, err)
		defer os.RemoveAll(tempDir)

		results, truncated, err := GlobWithDoublestar("", tempDir, 0)
		require.NoError(t, err)
		assert.False(t, truncated)
		assert.Equal(t, 0, len(results))
	})
}

func TestFileInfo(t *testing.T) {
	now := time.Now()
	fi := FileInfo{
		Path:    "/test/path",
		ModTime: now,
	}

	assert.Equal(t, "/test/path", fi.Path)
	assert.Equal(t, now, fi.ModTime)
}

func TestInit(t *testing.T) {
	// Test that init function sets rgPath and fzfPath
	// This is a bit tricky to test directly since init() runs before tests
	// We can test by checking if the paths are set when the binaries exist

	// Test rg
	if _, err := exec.LookPath("rg"); err == nil {
		// rg is available, rgPath should be set
		assert.NotEmpty(t, rgPath)
	} else {
		// rg is not available, rgPath should be empty
		assert.Empty(t, rgPath)
	}

	// Test fzf
	if _, err := exec.LookPath("fzf"); err == nil {
		// fzf is available, fzfPath should be set
		assert.NotEmpty(t, fzfPath)
	} else {
		// fzf is not available, fzfPath should be empty
		assert.Empty(t, fzfPath)
	}
}

func TestGlobWithDoublestar_Sorting(t *testing.T) {
	// Create a temporary directory for testing
	tempDir, err := os.MkdirTemp("", "fileutil_test_sort")
	require.NoError(t, err)
	defer os.RemoveAll(tempDir)

	// Create test files with different modification times
	testFiles := []string{
		"old.go",
		"new.go",
		"middle.go",
	}

	for i, file := range testFiles {
		fullPath := filepath.Join(tempDir, file)
		err := os.WriteFile(fullPath, []byte("test content"), 0644)
		require.NoError(t, err)
		
		// Set different modification times
		modTime := time.Now().Add(time.Duration(i) * time.Hour)
		err = os.Chtimes(fullPath, modTime, modTime)
		require.NoError(t, err)
	}

	results, truncated, err := GlobWithDoublestar("**/*.go", tempDir, 0)
	require.NoError(t, err)
	assert.False(t, truncated)
	assert.Equal(t, 3, len(results))

	// Results should be sorted by modification time (newest first)
	// So we expect: middle.go, new.go, old.go
	expectedOrder := []string{"middle.go", "new.go", "old.go"}
	for i, result := range results {
		filename := filepath.Base(result)
		assert.Equal(t, expectedOrder[i], filename)
	}
}

func TestGlobWithDoublestar_SearchPathHandling(t *testing.T) {
	// Create a temporary directory for testing
	tempDir, err := os.MkdirTemp("", "fileutil_test_path")
	require.NoError(t, err)
	defer os.RemoveAll(tempDir)

	// Create test file
	testFile := "test.go"
	fullPath := filepath.Join(tempDir, testFile)
	err = os.WriteFile(fullPath, []byte("test content"), 0644)
	require.NoError(t, err)

	tests := []struct {
		name       string
		searchPath string
	}{
		{
			name:       "with absolute path",
			searchPath: tempDir,
		},
		{
			name:       "with current directory",
			searchPath: ".",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var results []string
			var truncated bool
			var err error

			if tt.searchPath == "." {
				// Change to temp directory for this test
				originalDir, err := os.Getwd()
				require.NoError(t, err)
				defer os.Chdir(originalDir)
				
				err = os.Chdir(tempDir)
				require.NoError(t, err)
			}

			results, truncated, err = GlobWithDoublestar("**/*.go", tt.searchPath, 0)
			require.NoError(t, err)
			assert.False(t, truncated)
			assert.Equal(t, 1, len(results))
			
			// Check that the result path is correctly formatted
			assert.True(t, strings.HasSuffix(results[0], testFile))
		})
	}
}
